# 贪吃蛇游戏

一个使用HTML5 Canvas和JavaScript开发的经典贪吃蛇游戏。

## 功能特点

- 🐍 经典贪吃蛇游戏玩法
- 🎮 键盘方向键控制
- ⏸️ 暂停/继续功能
- 🏆 分数统计和最高分记录
- 📱 响应式设计，支持移动设备
- 🎨 现代化UI设计
- 💾 本地存储最高分记录

## 游戏控制

- **方向键**: 控制蛇的移动方向
- **空格键**: 暂停/继续游戏
- **开始游戏**: 点击按钮开始新游戏
- **暂停**: 点击按钮暂停当前游戏
- **重新开始**: 点击按钮重置游戏

## 游戏规则

1. 控制蛇吃掉黄色的食物
2. 每吃掉一个食物，蛇会变长，得分增加10分
3. 避免撞到墙壁或蛇的身体
4. 游戏结束后会显示最终得分和历史最高分

## 文件结构

```
├── index.html          # 主页面文件
├── style.css           # 样式文件
├── script.js           # 游戏逻辑文件
├── tests/              # 测试文件夹
│   └── test.html       # 游戏功能测试页面
└── README.md           # 说明文档
```

## 如何运行

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器运行（推荐）

### 使用本地服务器运行

```bash
# 使用Python 3
python -m http.server 8000

# 使用Python 2
python -m SimpleHTTPServer 8000

# 使用Node.js (需要安装http-server)
npx http-server

# 使用PHP
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000`

## 测试

打开 `tests/test.html` 文件可以运行游戏功能测试，包括：

- 游戏初始化测试
- 蛇移动测试
- 食物生成测试
- 碰撞检测测试
- 计分系统测试

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式和动画
- **JavaScript (ES6+)**: 游戏逻辑
- **Canvas API**: 游戏渲染
- **LocalStorage**: 数据持久化

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 主要类和方法

- `SnakeGame`: 主游戏类
  - `init()`: 初始化游戏
  - `startGame()`: 开始游戏
  - `gameLoop()`: 游戏主循环
  - `update()`: 更新游戏状态
  - `draw()`: 渲染游戏画面
  - `generateFood()`: 生成食物
  - `gameOver()`: 游戏结束处理

### 自定义配置

可以在 `script.js` 中修改以下参数：

- `gridSize`: 网格大小（默认20px）
- `gameLoop` 中的延迟时间（默认150ms）
- 分数增加值（默认10分）

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个游戏！

## 更新日志

### v1.0.0
- 基本游戏功能实现
- 响应式设计
- 本地最高分存储
- 游戏测试套件
